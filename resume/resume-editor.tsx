import React, { useState } from 'react';
import { Plus, Trash2, Save, User, GraduationCap, Briefcase, Code, Award, FileText } from 'lucide-react';

const ResumeEditor = () => {
  const [resumeData, setResumeData] = useState({
    selectedTemplate: 1,
    headings: {
      skills: "Technical Skills",
      work: "Professional Experience"
    },
    basics: {
      email: "<EMAIL>",
      name: "<PERSON><PERSON>",
      phone: "************",
      location: {
        address: "Toronto, ON"
      }
    },
    education: [
      {
        institution: "The Data Incubator",
        studyType: "Data Scientist Certification",
        endDate: "Nov 2021",
        area: "Fellowship Program"
      },
      {
        area: "Mathematics",
        endDate: "June 2019",
        institution: "University of Toronto",
        studyType: "PhD"
      },
      {
        area: "Applied Mathematics",
        endDate: "June 2014",
        institution: "University of Waterloo",
        studyType: "MS"
      },
      {
        area: "Mathematics, minor in computer science",
        endDate: "June 2012",
        institution: "University of Waterloo",
        studyType: "BS"
      }
    ],
    work: [
      {
        highlights: [
          "Used deep learning models , such as classifiers, recurrent neural networks, and generative adversarial networks (GANs) to improve the touch sensitivity of Huawei's phones and tablets. Libraries used included PyTorch and OpenCV. Also took responsibility for the whole pipeline including data collection and productionizing the model on Android or Windows devices.",
          "Improved heavy touch model performance 10x by reducing latency from 1.3 ms per frame to less than 170 micros using\nonnx runtime. Models are typically deployed using torchscript.",
          "Improved GAN training time over 15x using PyTorch distributed data parallel training. This allowed us to work efficiently with large datasets containing 10s of thousands of samples."
        ],
        company: "Huawei",
        position: "Data Scientist",
        startDate: "September 2022",
        endDate: "August 2023"
      },
      {
        company: "Natural resources Canada",
        endDate: "November 2021",
        highlights: [
          "Improved the Open Science Data Platform by developing a topic model in python using scikit-learn to automatically extract keywords from publication abstracts, and thereby improved the indexing capabilities of the database.",
          "Saved $40K in consulting fees by productionizing Tensorflow based lichen segmentation tool with ESRI ArcGIS online database using python and SQL.",
          "Helped a team at the Canda Centre for Mapping and Earth Observation move to a cloud native software infrastructure using Docker.",
          "Adapated to COVID-19 restrictions by coordinating remote work using Lambda workstations and developing interactive map using python of Indigenous communities affected by major projects."
        ],
        location: "",
        position: "Data scientist",
        startDate: "November 2019",
        website: ""
      }
    ],
    skills: [
      {
        keywords: [
          "Python (Scikit-Learn, NumPy, Pandas, Flask)",
          "C++",
          "SQL"
        ],
        name: "Programming Languages"
      },
      {
        keywords: [
          "Deep Learning (PyTorch)",
          "Natural Language Processing (LLM Fine-tuning, Sentiment analysis, NER, Text summarization) ",
          "Pandas",
          "Scikit-Learn",
          "Numpy",
          "Git",
          "Docker",
          "MLOps"
        ],
        name: "Data Science"
      },
      {
        keywords: [
          "Client Facing Consulting",
          "Team leadership"
        ],
        name: "Industry Knowledge"
      },
      {
        keywords: [
          "TensorFlow",
          "Deep Learning",
          "Natural language processing",
          "SVM",
          "Naive Bayes",
          "Neural Networks (CNN | RNN | LSTM)",
          "Unsupervised Learning (K-means | PCA)",
          "Classification"
        ],
        name: "Machine Learning"
      }
    ],
    projects: [
      {
        keywords: [""],
        name: "Dialogue summarization",
        description: "- Compared zero shot inference, prompt engineering, and few shot inference for dialogue summarization with the FLAN-T5 model."
      },
      {
        keywords: [""],
        name: "Disaster tweet Kaggle competition",
        url: "github.com/krishanr/disaster_tweets",
        description: "- Placed in the top 10% of competitors using a transformer-based sentiment analysis model to automatically detect disaster tweets.\n- A more traditional naive-bayes model was also built, which had a lower AUC score."
      },
      {
        keywords: [""],
        name: "Arxiv Explorer",
        url: "http://arxiv-explore.herokuapp.com/",
        description: "- My data incubator capstone project was an interactive web dashboard, called arxiv explorer, built using pandas, plotly and heroku, to allow users to explore influential preprints on various subcategories of arxiv. It's a powerful exploratory tool that helps new researchers to better understand their field."
      },
      {
        keywords: [""],
        name: "The Data Incubator Fellowship Program",
        description: "- Parsed, cleaned, and processed a 10 GB set of XML files of user actions on a Q&A website using PySpark. Using this data, answered questions about user behavior to predict the long-term behavior of new users. Trained a word2vec model and a classification model on tags associated with questions.\n- Built machine learning models to analyze text and predict star ratings of review data from Yelp. This involved using n-grams, tfidf scaling, regularization techniques, spacy for stop-word removal, and naïve bayes models."
      },
      {
        description: "- 5 papers in peer-reviewed scientific journals, including Journal of Mathematical Physics",
        name: "Papers"
      }
    ],
    awards: [
      {
        awarder: "",
        date: "",
        summary: "",
        title: ""
      }
    ],
    sections: [
      "profile",
      "education",
      "work",
      "projects",
      "skills"
    ]
  });

  const [activeSection, setActiveSection] = useState('basics');

  const updateBasics = (field, value) => {
    if (field === 'address') {
      setResumeData(prev => ({
        ...prev,
        basics: {
          ...prev.basics,
          location: { ...prev.basics.location, address: value }
        }
      }));
    } else {
      setResumeData(prev => ({
        ...prev,
        basics: { ...prev.basics, [field]: value }
      }));
    }
  };

  const addEducation = () => {
    setResumeData(prev => ({
      ...prev,
      education: [...prev.education, {
        institution: "",
        studyType: "",
        endDate: "",
        area: "",
        startDate: ""
      }]
    }));
  };

  const updateEducation = (index, field, value) => {
    setResumeData(prev => ({
      ...prev,
      education: prev.education.map((edu, i) => 
        i === index ? { ...edu, [field]: value } : edu
      )
    }));
  };

  const removeEducation = (index) => {
    setResumeData(prev => ({
      ...prev,
      education: prev.education.filter((_, i) => i !== index)
    }));
  };

  const addWork = () => {
    setResumeData(prev => ({
      ...prev,
      work: [...prev.work, {
        company: "",
        position: "",
        startDate: "",
        endDate: "",
        highlights: [""],
        location: "",
        website: ""
      }]
    }));
  };

  const updateWork = (index, field, value) => {
    setResumeData(prev => ({
      ...prev,
      work: prev.work.map((job, i) => 
        i === index ? { ...job, [field]: value } : job
      )
    }));
  };

  const updateWorkHighlight = (jobIndex, highlightIndex, value) => {
    setResumeData(prev => ({
      ...prev,
      work: prev.work.map((job, i) => 
        i === jobIndex ? {
          ...job,
          highlights: job.highlights.map((highlight, j) => 
            j === highlightIndex ? value : highlight
          )
        } : job
      )
    }));
  };

  const addWorkHighlight = (jobIndex) => {
    setResumeData(prev => ({
      ...prev,
      work: prev.work.map((job, i) => 
        i === jobIndex ? { ...job, highlights: [...job.highlights, ""] } : job
      )
    }));
  };

  const removeWorkHighlight = (jobIndex, highlightIndex) => {
    setResumeData(prev => ({
      ...prev,
      work: prev.work.map((job, i) => 
        i === jobIndex ? {
          ...job,
          highlights: job.highlights.filter((_, j) => j !== highlightIndex)
        } : job
      )
    }));
  };

  const removeWork = (index) => {
    setResumeData(prev => ({
      ...prev,
      work: prev.work.filter((_, i) => i !== index)
    }));
  };

  const addSkillCategory = () => {
    setResumeData(prev => ({
      ...prev,
      skills: [...prev.skills, { name: "", keywords: [""] }]
    }));
  };

  const updateSkillCategory = (index, field, value) => {
    setResumeData(prev => ({
      ...prev,
      skills: prev.skills.map((skill, i) => 
        i === index ? { ...skill, [field]: value } : skill
      )
    }));
  };

  const updateSkillKeyword = (categoryIndex, keywordIndex, value) => {
    setResumeData(prev => ({
      ...prev,
      skills: prev.skills.map((skill, i) => 
        i === categoryIndex ? {
          ...skill,
          keywords: skill.keywords.map((keyword, j) => 
            j === keywordIndex ? value : keyword
          )
        } : skill
      )
    }));
  };

  const addSkillKeyword = (categoryIndex) => {
    setResumeData(prev => ({
      ...prev,
      skills: prev.skills.map((skill, i) => 
        i === categoryIndex ? { ...skill, keywords: [...skill.keywords, ""] } : skill
      )
    }));
  };

  const removeSkillKeyword = (categoryIndex, keywordIndex) => {
    setResumeData(prev => ({
      ...prev,
      skills: prev.skills.map((skill, i) => 
        i === categoryIndex ? {
          ...skill,
          keywords: skill.keywords.filter((_, j) => j !== keywordIndex)
        } : skill
      )
    }));
  };

  const removeSkillCategory = (index) => {
    setResumeData(prev => ({
      ...prev,
      skills: prev.skills.filter((_, i) => i !== index)
    }));
  };

  const addProject = () => {
    setResumeData(prev => ({
      ...prev,
      projects: [...prev.projects, {
        name: "",
        description: "",
        url: "",
        keywords: [""]
      }]
    }));
  };

  const updateProject = (index, field, value) => {
    setResumeData(prev => ({
      ...prev,
      projects: prev.projects.map((project, i) => 
        i === index ? { ...project, [field]: value } : project
      )
    }));
  };

  const removeProject = (index) => {
    setResumeData(prev => ({
      ...prev,
      projects: prev.projects.filter((_, i) => i !== index)
    }));
  };

  const exportJSON = () => {
    const dataStr = JSON.stringify(resumeData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'resume.json';
    link.click();
  };

  const sectionIcons = {
    basics: User,
    education: GraduationCap,
    work: Briefcase,
    skills: Code,
    projects: FileText,
    awards: Award
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="flex">
        {/* Sidebar */}
        <div className="w-64 bg-white shadow-lg h-screen sticky top-0">
          <div className="p-6 border-b">
            <h1 className="text-xl font-bold text-gray-800">Resume Editor</h1>
          </div>
          <nav className="p-4">
            {Object.entries(sectionIcons).map(([section, Icon]) => (
              <button
                key={section}
                onClick={() => setActiveSection(section)}
                className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg mb-2 transition-colors ${
                  activeSection === section
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-600 hover:bg-gray-100'
                }`}
              >
                <Icon size={20} />
                <span className="capitalize">{section}</span>
              </button>
            ))}
          </nav>
          <div className="p-4 border-t">
            <button
              onClick={exportJSON}
              className="w-full flex items-center justify-center space-x-2 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors"
            >
              <Save size={20} />
              <span>Export JSON</span>
            </button>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 p-8">
          {activeSection === 'basics' && (
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-2xl font-bold mb-6 flex items-center">
                <User className="mr-3" />
                Personal Information
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Name</label>
                  <input
                    type="text"
                    value={resumeData.basics.name}
                    onChange={(e) => updateBasics('name', e.target.value)}
                    className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Email</label>
                  <input
                    type="email"
                    value={resumeData.basics.email}
                    onChange={(e) => updateBasics('email', e.target.value)}
                    className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Phone</label>
                  <input
                    type="tel"
                    value={resumeData.basics.phone}
                    onChange={(e) => updateBasics('phone', e.target.value)}
                    className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Address</label>
                  <input
                    type="text"
                    value={resumeData.basics.location.address}
                    onChange={(e) => updateBasics('address', e.target.value)}
                    className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>
            </div>
          )}

          {activeSection === 'education' && (
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-2xl font-bold flex items-center">
                  <GraduationCap className="mr-3" />
                  Education
                </h2>
                <button
                  onClick={addEducation}
                  className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center"
                >
                  <Plus size={20} className="mr-2" />
                  Add Education
                </button>
              </div>
              {resumeData.education.map((edu, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-4 mb-4">
                  <div className="flex justify-between items-start mb-4">
                    <h3 className="text-lg font-semibold">Education {index + 1}</h3>
                    <button
                      onClick={() => removeEducation(index)}
                      className="text-red-600 hover:text-red-800"
                    >
                      <Trash2 size={20} />
                    </button>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Institution</label>
                      <input
                        type="text"
                        value={edu.institution}
                        onChange={(e) => updateEducation(index, 'institution', e.target.value)}
                        className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Degree Type</label>
                      <input
                        type="text"
                        value={edu.studyType}
                        onChange={(e) => updateEducation(index, 'studyType', e.target.value)}
                        className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Field of Study</label>
                      <input
                        type="text"
                        value={edu.area}
                        onChange={(e) => updateEducation(index, 'area', e.target.value)}
                        className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">End Date</label>
                      <input
                        type="text"
                        value={edu.endDate}
                        onChange={(e) => updateEducation(index, 'endDate', e.target.value)}
                        className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {activeSection === 'work' && (
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-2xl font-bold flex items-center">
                  <Briefcase className="mr-3" />
                  Work Experience
                </h2>
                <button
                  onClick={addWork}
                  className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center"
                >
                  <Plus size={20} className="mr-2" />
                  Add Job
                </button>
              </div>
              {resumeData.work.map((job, jobIndex) => (
                <div key={jobIndex} className="border border-gray-200 rounded-lg p-4 mb-6">
                  <div className="flex justify-between items-start mb-4">
                    <h3 className="text-lg font-semibold">Job {jobIndex + 1}</h3>
                    <button
                      onClick={() => removeWork(jobIndex)}
                      className="text-red-600 hover:text-red-800"
                    >
                      <Trash2 size={20} />
                    </button>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Company</label>
                      <input
                        type="text"
                        value={job.company}
                        onChange={(e) => updateWork(jobIndex, 'company', e.target.value)}
                        className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Position</label>
                      <input
                        type="text"
                        value={job.position}
                        onChange={(e) => updateWork(jobIndex, 'position', e.target.value)}
                        className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Start Date</label>
                      <input
                        type="text"
                        value={job.startDate}
                        onChange={(e) => updateWork(jobIndex, 'startDate', e.target.value)}
                        className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">End Date</label>
                      <input
                        type="text"
                        value={job.endDate}
                        onChange={(e) => updateWork(jobIndex, 'endDate', e.target.value)}
                        className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                  </div>
                  <div>
                    <div className="flex justify-between items-center mb-2">
                      <label className="block text-sm font-medium text-gray-700">Highlights</label>
                      <button
                        onClick={() => addWorkHighlight(jobIndex)}
                        className="text-blue-600 hover:text-blue-800 text-sm flex items-center"
                      >
                        <Plus size={16} className="mr-1" />
                        Add Highlight
                      </button>
                    </div>
                    {job.highlights.map((highlight, highlightIndex) => (
                      <div key={highlightIndex} className="flex gap-2 mb-2">
                        <textarea
                          value={highlight}
                          onChange={(e) => updateWorkHighlight(jobIndex, highlightIndex, e.target.value)}
                          className="flex-1 p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 min-h-[80px]"
                          rows={3}
                        />
                        <button
                          onClick={() => removeWorkHighlight(jobIndex, highlightIndex)}
                          className="text-red-600 hover:text-red-800"
                        >
                          <Trash2 size={16} />
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          )}

          {activeSection === 'skills' && (
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-2xl font-bold flex items-center">
                  <Code className="mr-3" />
                  Skills
                </h2>
                <button
                  onClick={addSkillCategory}
                  className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center"
                >
                  <Plus size={20} className="mr-2" />
                  Add Category
                </button>
              </div>
              {resumeData.skills.map((skillCategory, categoryIndex) => (
                <div key={categoryIndex} className="border border-gray-200 rounded-lg p-4 mb-4">
                  <div className="flex justify-between items-start mb-4">
                    <div className="flex-1 mr-4">
                      <label className="block text-sm font-medium text-gray-700 mb-2">Category Name</label>
                      <input
                        type="text"
                        value={skillCategory.name}
                        onChange={(e) => updateSkillCategory(categoryIndex, 'name', e.target.value)}
                        className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                    <button
                      onClick={() => removeSkillCategory(categoryIndex)}
                      className="text-red-600 hover:text-red-800 mt-8"
                    >
                      <Trash2 size={20} />
                    </button>
                  </div>
                  <div>
                    <div className="flex justify-between items-center mb-2">
                      <label className="block text-sm font-medium text-gray-700">Skills</label>
                      <button
                        onClick={() => addSkillKeyword(categoryIndex)}
                        className="text-blue-600 hover:text-blue-800 text-sm flex items-center"
                      >
                        <Plus size={16} className="mr-1" />
                        Add Skill
                      </button>
                    </div>
                    {skillCategory.keywords.map((keyword, keywordIndex) => (
                      <div key={keywordIndex} className="flex gap-2 mb-2">
                        <input
                          type="text"
                          value={keyword}
                          onChange={(e) => updateSkillKeyword(categoryIndex, keywordIndex, e.target.value)}
                          className="flex-1 p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        />
                        <button
                          onClick={() => removeSkillKeyword(categoryIndex, keywordIndex)}
                          className="text-red-600 hover:text-red-800"
                        >
                          <Trash2 size={16} />
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          )}

          {activeSection === 'projects' && (
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-2xl font-bold flex items-center">
                  <FileText className="mr-3" />
                  Projects
                </h2>
                <button
                  onClick={addProject}
                  className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center"
                >
                  <Plus size={20} className="mr-2" />
                  Add Project
                </button>
              </div>
              {resumeData.projects.map((project, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-4 mb-4">
                  <div className="flex justify-between items-start mb-4">
                    <h3 className="text-lg font-semibold">Project {index + 1}</h3>
                    <button
                      onClick={() => removeProject(index)}
                      className="text-red-600 hover:text-red-800"
                    >
                      <Trash2 size={20} />
                    </button>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Project Name</label>
                      <input
                        type="text"
                        value={project.name}
                        onChange={(e) => updateProject(index, 'name', e.target.value)}
                        className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">URL (optional)</label>
                      <input
                        type="url"
                        value={project.url || ''}
                        onChange={(e) => updateProject(index, 'url', e.target.value)}
                        className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
                    <textarea
                      value={project.description}
                      onChange={(e) => updateProject(index, 'description', e.target.value)}
                      className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 min-h-[100px]"
                      rows={4}
                    />
                  </div>
                </div>
              ))}
            </div>
          )}

          {activeSection === 'awards' && (
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-2xl font-bold flex items-center">
                  <Award className="mr-3" />
                  Awards & Achievements
                </h2>
                <button
                  onClick={() => setResumeData(prev => ({
                    ...prev,
                    awards: [...prev.awards, { title: "", awarder: "", date: "", summary: "" }]
                  }))}
                  className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center"
                >
                  <Plus size={20} className="mr-2" />
                  Add Award
                </button>
              </div>
              {resumeData.awards.map((award, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-4 mb-4">
                  <div className="flex justify-between items-start mb-4">
                    <h3 className="text-lg font-semibold">Award {index + 1}</h3>
                    <button
                      onClick={() => setResumeData(prev => ({
                        ...prev,
                        awards: prev.awards.filter((_, i) => i !== index)
                      }))}
                      className="text-red-600 hover:text-red-800"
                    >
                      <Trash2 size={20} />
                    </button>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Title</label>
                      <input
                        type="text"
                        value={award.title}
                        onChange={(e) => setResumeData(prev => ({
                          ...prev,
                          awards: prev.awards.map((a, i) => 
                            i === index ? { ...a, title: e.target.value } : a
                          )
                        }))}
                        className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Awarder</label>
                      <input
                        type="text"
                        value={award.awarder}
                        onChange={(e) => setResumeData(prev => ({
                          ...prev,
                          awards: prev.awards.map((a, i) => 
                            i === index ? { ...a, awarder: e.target.value } : a
                          )
                        }))}
                        className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Date</label>
                      <input
                        type="text"
                        value={award.date}
                        onChange={(e) => setResumeData(prev => ({
                          ...prev,
                          awards: prev.awards.map((a, i) => 
                            i === index ? { ...a, date: e.target.value } : a
                          )
                        }))}
                        className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Summary</label>
                    <textarea
                      value={award.summary}
                      onChange={(e) => setResumeData(prev => ({
                        ...prev,
                        awards: prev.awards.map((a, i) => 
                          i === index ? { ...a, summary: e.target.value } : a
                        )
                      }))}
                      className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 min-h-[80px]"
                      rows={3}
                    />
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ResumeEditor; 