import React, { useState, useEffect, useRef } from 'react';
import { Document, Page, Text, View, StyleSheet, PDFViewer, PDFDownloadLink, Font } from '@react-pdf/renderer';
import { FileText, Download, Eye, Code, Plus, Trash2, Save, Upload, User, GraduationCap, Briefcase, Award } from 'lucide-react';

// PDF Styles
const styles = StyleSheet.create({
  page: {
    flexDirection: 'column',
    backgroundColor: '#FFFFFF',
    padding: 30,
    fontFamily: 'Helvetica',
  },
  header: {
    marginBottom: 20,
    textAlign: 'center',
    borderBottom: 1,
    borderBottomColor: '#333333',
    paddingBottom: 10,
  },
  name: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 5,
    color: '#2F4F4F',
  },
  contactInfo: {
    fontSize: 10,
    marginBottom: 2,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#2F4F4F',
    marginTop: 15,
    marginBottom: 8,
    borderBottom: 1,
    borderBottomColor: '#2F4F4F',
    paddingBottom: 2,
  },
  summaryText: {
    fontSize: 11,
    lineHeight: 1.4,
    textAlign: 'justify',
  },
  jobHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 3,
  },
  jobTitle: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  jobDuration: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  companyInfo: {
    fontSize: 11,
    fontStyle: 'italic',
    marginBottom: 5,
  },
  achievementItem: {
    fontSize: 10,
    marginBottom: 3,
    marginLeft: 15,
    lineHeight: 1.3,
  },
  educationItem: {
    marginBottom: 10,
  },
  educationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 3,
  },
  degree: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  institution: {
    fontSize: 11,
    fontStyle: 'italic',
  },
  skillsText: {
    fontSize: 11,
    marginBottom: 5,
    lineHeight: 1.3,
  },
  projectItem: {
    marginBottom: 8,
  },
  projectHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 3,
  },
  projectName: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  projectLink: {
    fontSize: 9,
    color: '#0066CC',
  },
  projectDescription: {
    fontSize: 10,
    lineHeight: 1.3,
  },
});

// Convert resume data to the format expected by PDF component
const convertToResumeFormat = (data) => {
  return {
    personalInfo: {
      name: data.basics?.name || '',
      email: data.basics?.email || '',
      phone: data.basics?.phone || '',
      location: data.basics?.location?.address || '',
      linkedin: '', // Not in original format
      website: '' // Not in original format
    },
    summary: '', // Not in original format
    experience: data.work?.map(job => ({
      company: job.company || '',
      position: job.position || '',
      duration: `${job.startDate || ''} - ${job.endDate || ''}`,
      location: job.location || '',
      achievements: job.highlights || []
    })) || [],
    education: data.education?.map(edu => ({
      institution: edu.institution || '',
      degree: edu.studyType || '',
      duration: edu.endDate || '',
      area: edu.area || '',
      gpa: edu.gpa || ''
    })) || [],
    skills: {
      technical: data.skills?.find(s => s.name.toLowerCase().includes('technical') || s.name.toLowerCase().includes('programming'))?.keywords || [],
      soft: data.skills?.find(s => s.name.toLowerCase().includes('soft') || s.name.toLowerCase().includes('industry'))?.keywords || []
    },
    projects: data.projects?.map(project => ({
      name: project.name || '',
      description: project.description || '',
      technologies: project.keywords || [],
      link: project.url || ''
    })) || []
  };
};

// PDF Document Component
const ResumePDF = ({ data }) => {
  const convertedData = convertToResumeFormat(data);
  
  return (
    <Document>
      <Page size="A4" style={styles.page}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.name}>{convertedData.personalInfo?.name || 'Your Name'}</Text>
          {convertedData.personalInfo?.email && (
            <Text style={styles.contactInfo}>
              {convertedData.personalInfo.email}
              {convertedData.personalInfo.phone && ` • ${convertedData.personalInfo.phone}`}
              {convertedData.personalInfo.location && ` • ${convertedData.personalInfo.location}`}
            </Text>
          )}
        </View>

        {/* Professional Experience */}
        {convertedData.experience && convertedData.experience.length > 0 && (
          <View>
            <Text style={styles.sectionTitle}>PROFESSIONAL EXPERIENCE</Text>
            {convertedData.experience.map((exp, index) => (
              <View key={index} style={{ marginBottom: 12 }}>
                <View style={styles.jobHeader}>
                  <Text style={styles.jobTitle}>{exp.position}</Text>
                  <Text style={styles.jobDuration}>{exp.duration}</Text>
                </View>
                <Text style={styles.companyInfo}>
                  {exp.company}{exp.location && ` • ${exp.location}`}
                </Text>
                {exp.achievements && exp.achievements.map((achievement, achIndex) => (
                  <Text key={achIndex} style={styles.achievementItem}>
                    • {achievement}
                  </Text>
                ))}
              </View>
            ))}
          </View>
        )}

        {/* Education */}
        {convertedData.education && convertedData.education.length > 0 && (
          <View>
            <Text style={styles.sectionTitle}>EDUCATION</Text>
            {convertedData.education.map((edu, index) => (
              <View key={index} style={styles.educationItem}>
                <View style={styles.educationHeader}>
                  <Text style={styles.degree}>{edu.degree} {edu.area && `in ${edu.area}`}</Text>
                  <Text style={styles.jobDuration}>{edu.duration}</Text>
                </View>
                <Text style={styles.institution}>
                  {edu.institution}{edu.gpa && ` • GPA: ${edu.gpa}`}
                </Text>
              </View>
            ))}
          </View>
        )}

        {/* Technical Skills */}
        {(convertedData.skills.technical.length > 0 || convertedData.skills.soft.length > 0) && (
          <View>
            <Text style={styles.sectionTitle}>SKILLS</Text>
            {data.skills && data.skills.map((skillCategory, index) => (
              skillCategory.keywords && skillCategory.keywords.length > 0 && (
                <Text key={index} style={styles.skillsText}>
                  <Text style={{ fontWeight: 'bold' }}>{skillCategory.name}: </Text>
                  {skillCategory.keywords.filter(k => k.trim()).join(', ')}
                </Text>
              )
            ))}
          </View>
        )}

        {/* Projects */}
        {convertedData.projects && convertedData.projects.length > 0 && (
          <View>
            <Text style={styles.sectionTitle}>PROJECTS</Text>
            {convertedData.projects.map((project, index) => (
              <View key={index} style={styles.projectItem}>
                <View style={styles.projectHeader}>
                  <Text style={styles.projectName}>{project.name}</Text>
                  {project.link && (
                    <Text style={styles.projectLink}>{project.link}</Text>
                  )}
                </View>
                <Text style={styles.projectDescription}>
                  {project.description}
                </Text>
              </View>
            ))}
          </View>
        )}
      </Page>
    </Document>
  );
};

const ResumeLatexGenerator = () => {
  const fileInputRef = useRef(null);
  const [importError, setImportError] = useState('');
  const [importSuccess, setImportSuccess] = useState('');
  const [generatedLatex, setGeneratedLatex] = useState('');
  const [showPDF, setShowPDF] = useState(false);
  const [activeSection, setActiveSection] = useState('basics');
  const [viewMode, setViewMode] = useState('editor'); // 'editor' or 'json'

  const [resumeData, setResumeData] = useState({
    selectedTemplate: 1,
    headings: {
      skills: "Technical Skills",
      work: "Professional Experience"
    },
    basics: {
      email: "",
      name: "",
      phone: "",
      location: {
        address: ""
      }
    },
    education: [],
    work: [],
    skills: [],
    projects: [],
    awards: [],
    sections: [
      "profile",
      "education",
      "work",
      "projects",
      "skills"
    ]
  });

  const generateLatex = (jsonData) => {
    const convertedData = convertToResumeFormat(jsonData);
    
    const latex = `\\documentclass[11pt,letterpaper]{article}
\\usepackage[utf8]{inputenc}
\\usepackage[margin=0.75in]{geometry}
\\usepackage{enumitem}
\\usepackage{titlesec}
\\usepackage{hyperref}
\\usepackage{xcolor}

% Define colors
\\definecolor{headercolor}{RGB}{47, 79, 79}
\\definecolor{linkcolor}{RGB}{0, 100, 200}

% Format section headers
\\titleformat{\\section}{\\large\\bfseries\\color{headercolor}}{}{0em}{}[\\titlerule]
\\titlespacing{\\section}{0pt}{12pt}{6pt}

% Remove page numbers
\\pagestyle{empty}

% Customize hyperlink colors
\\hypersetup{
    colorlinks=true,
    linkcolor=linkcolor,
    urlcolor=linkcolor,
    pdftitle={${convertedData.personalInfo?.name || 'Resume'}}
}

\\begin{document}

% Header
\\begin{center}
{\\LARGE\\bfseries ${convertedData.personalInfo?.name || 'Your Name'}}\\\\[4pt]
${convertedData.personalInfo?.email ? `\\href{mailto:${convertedData.personalInfo.email}}{${convertedData.personalInfo.email}}` : ''} 
${convertedData.personalInfo?.phone ? ` \\textbullet\\ ${convertedData.personalInfo.phone}` : ''}
${convertedData.personalInfo?.location ? ` \\textbullet\\ ${convertedData.personalInfo.location}` : ''}
\\end{center}

${convertedData.experience && convertedData.experience.length > 0 ? `\\section{Professional Experience}
${convertedData.experience.map(exp => `
\\textbf{${exp.position}} \\hfill ${exp.duration}\\\\
\\textit{${exp.company}} ${exp.location ? `\\hfill ${exp.location}` : ''}
${exp.achievements && exp.achievements.length > 0 ? `\\begin{itemize}[leftmargin=*,noitemsep,topsep=2pt]
${exp.achievements.map(achievement => `\\item ${achievement.replace(/\n/g, ' ')}`).join('\n')}
\\end{itemize}` : ''}
`).join('\n')}

` : ''}

${convertedData.education && convertedData.education.length > 0 ? `\\section{Education}
${convertedData.education.map(edu => `
\\textbf{${edu.degree}} ${edu.area ? `in ${edu.area}` : ''} \\hfill ${edu.duration}\\\\
\\textit{${edu.institution}} ${edu.gpa ? `\\hfill GPA: ${edu.gpa}` : ''}
`).join('\n')}

` : ''}

${jsonData.skills && jsonData.skills.length > 0 ? `\\section{Skills}
${jsonData.skills.map(skillCategory => `
${skillCategory.keywords && skillCategory.keywords.length > 0 ? `\\textbf{${skillCategory.name}:} ${skillCategory.keywords.filter(k => k.trim()).join(', ')}\\\\[4pt]` : ''}
`).join('\n')}

` : ''}

${convertedData.projects && convertedData.projects.length > 0 ? `\\section{Projects}
${convertedData.projects.map(project => `
\\textbf{${project.name}} ${project.link ? `\\hfill \\href{https://${project.link}}{${project.link}}` : ''}\\\\
${project.description.replace(/\n/g, ' ')}
`).join('\n')}` : ''}

\\end{document}`;

    return latex;
  };

  const handleGeneratePDF = () => {
    try {
      const latex = generateLatex(resumeData);
      setGeneratedLatex(latex);
      setShowPDF(true);
    } catch (error) {
      alert('Error generating PDF: ' + error.message);
    }
  };

  const downloadLatex = () => {
    if (!generatedLatex) return;
    
    const blob = new Blob([generatedLatex], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'resume.tex';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const exportJSON = () => {
    const dataStr = JSON.stringify(resumeData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'resume.json';
    link.click();
  };

  const importJSON = () => {
    fileInputRef.current?.click();
  };

  const handleFileImport = (event) => {
    const file = event.target.files[0];
    if (!file) return;

    setImportError('');
    setImportSuccess('');

    if (file.type !== 'application/json') {
      setImportError('Please select a valid JSON file.');
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const jsonData = JSON.parse(e.target.result);
        
        if (!jsonData.basics || !jsonData.basics.name) {
          setImportError('Invalid resume format. Missing required basic information.');
          return;
        }

        const defaultResumeData = {
          selectedTemplate: 1,
          headings: {
            skills: "Technical Skills",
            work: "Professional Experience"
          },
          basics: {
            email: "",
            name: "",
            phone: "",
            location: { address: "" }
          },
          education: [],
          work: [],
          skills: [],
          projects: [],
          awards: [],
          sections: ["profile", "education", "work", "projects", "skills"]
        };

        const mergedData = {
          ...defaultResumeData,
          ...jsonData,
          basics: {
            ...defaultResumeData.basics,
            ...jsonData.basics,
            location: {
              ...defaultResumeData.basics.location,
              ...(jsonData.basics?.location || {})
            }
          },
          headings: {
            ...defaultResumeData.headings,
            ...(jsonData.headings || {})
          }
        };

        setResumeData(mergedData);
        setImportSuccess('Resume imported successfully!');
        event.target.value = '';
        setTimeout(() => setImportSuccess(''), 3000);
      } catch (error) {
        setImportError(`Error parsing JSON file: ${error.message}`);
      }
    };

    reader.onerror = () => {
      setImportError('Error reading file.');
    };

    reader.readAsText(file);
  };

  // Editor functions
  const updateBasics = (field, value) => {
    if (field === 'address') {
      setResumeData(prev => ({
        ...prev,
        basics: {
          ...prev.basics,
          location: { ...prev.basics.location, address: value }
        }
      }));
    } else {
      setResumeData(prev => ({
        ...prev,
        basics: { ...prev.basics, [field]: value }
      }));
    }
  };

  const addEducation = () => {
    setResumeData(prev => ({
      ...prev,
      education: [...prev.education, {
        institution: "",
        studyType: "",
        endDate: "",
        area: "",
        startDate: ""
      }]
    }));
  };

  const updateEducation = (index, field, value) => {
    setResumeData(prev => ({
      ...prev,
      education: prev.education.map((edu, i) => 
        i === index ? { ...edu, [field]: value } : edu
      )
    }));
  };

  const removeEducation = (index) => {
    setResumeData(prev => ({
      ...prev,
      education: prev.education.filter((_, i) => i !== index)
    }));
  };

  const addWork = () => {
    setResumeData(prev => ({
      ...prev,
      work: [...prev.work, {
        company: "",
        position: "",
        startDate: "",
        endDate: "",
        highlights: [""],
        location: "",
        website: ""
      }]
    }));
  };

  const updateWork = (index, field, value) => {
    setResumeData(prev => ({
      ...prev,
      work: prev.work.map((job, i) => 
        i === index ? { ...job, [field]: value } : job
      )
    }));
  };

  const updateWorkHighlight = (jobIndex, highlightIndex, value) => {
    setResumeData(prev => ({
      ...prev,
      work: prev.work.map((job, i) => 
        i === jobIndex ? {
          ...job,
          highlights: job.highlights.map((highlight, j) => 
            j === highlightIndex ? value : highlight
          )
        } : job
      )
    }));
  };

  const addWorkHighlight = (jobIndex) => {
    setResumeData(prev => ({
      ...prev,
      work: prev.work.map((job, i) => 
        i === jobIndex ? { ...job, highlights: [...job.highlights, ""] } : job
      )
    }));
  };

  const removeWorkHighlight = (jobIndex, highlightIndex) => {
    setResumeData(prev => ({
      ...prev,
      work: prev.work.map((job, i) => 
        i === jobIndex ? {
          ...job,
          highlights: job.highlights.filter((_, j) => j !== highlightIndex)
        } : job
      )
    }));
  };

  const removeWork = (index) => {
    setResumeData(prev => ({
      ...prev,
      work: prev.work.filter((_, i) => i !== index)
    }));
  };

  const addSkillCategory = () => {
    setResumeData(prev => ({
      ...prev,
      skills: [...prev.skills, { name: "", keywords: [""] }]
    }));
  };

  const updateSkillCategory = (index, field, value) => {
    setResumeData(prev => ({
      ...prev,
      skills: prev.skills.map((skill, i) => 
        i === index ? { ...skill, [field]: value } : skill
      )
    }));
  };

  const updateSkillKeyword = (categoryIndex, keywordIndex, value) => {
    setResumeData(prev => ({
      ...prev,
      skills: prev.skills.map((skill, i) => 
        i === categoryIndex ? {
          ...skill,
          keywords: skill.keywords.map((keyword, j) => 
            j === keywordIndex ? value : keyword
          )
        } : skill
      )
    }));
  };

  const addSkillKeyword = (categoryIndex) => {
    setResumeData(prev => ({
      ...prev,
      skills: prev.skills.map((skill, i) => 
        i === categoryIndex ? { ...skill, keywords: [...skill.keywords, ""] } : skill
      )
    }));
  };

  const removeSkillKeyword = (categoryIndex, keywordIndex) => {
    setResumeData(prev => ({
      ...prev,
      skills: prev.skills.map((skill, i) => 
        i === categoryIndex ? {
          ...skill,
          keywords: skill.keywords.filter((_, j) => j !== keywordIndex)
        } : skill
      )
    }));
  };

  const removeSkillCategory = (index) => {
    setResumeData(prev => ({
      ...prev,
      skills: prev.skills.filter((_, i) => i !== index)
    }));
  };

  const addProject = () => {
    setResumeData(prev => ({
      ...prev,
      projects: [...prev.projects, {
        name: "",
        description: "",
        url: "",
        keywords: [""]
      }]
    }));
  };

  const updateProject = (index, field, value) => {
    setResumeData(prev => ({
      ...prev,
      projects: prev.projects.map((project, i) => 
        i === index ? { ...project, [field]: value } : project
      )
    }));
  };

  const removeProject = (index) => {
    setResumeData(prev => ({
      ...prev,
      projects: prev.projects.filter((_, i) => i !== index)
    }));
  };

  const sectionIcons = {
    basics: User,
    education: GraduationCap,
    work: Briefcase,
    skills: Code,
    projects: FileText,
    awards: Award
  };

  return (
    <div className="min-h-screen bg-gray-100">
      <div className="flex h-screen">
        {/* Left Panel - Editor */}
        <div className="w-1/2 bg-white shadow-lg flex flex-col">
          {/* Sidebar Navigation */}
          <div className="h-16 bg-gray-50 border-b flex items-center justify-between px-6">
            <h1 className="text-xl font-bold text-gray-800">Resume Builder</h1>
            <div className="flex gap-2">
              <button
                onClick={() => setViewMode(viewMode === 'editor' ? 'json' : 'editor')}
                className="flex items-center gap-1 px-3 py-1 bg-gray-500 text-white rounded text-sm hover:bg-gray-600"
              >
                <Code size={16} />
                {viewMode === 'editor' ? 'JSON' : 'Editor'}
              </button>
              <button
                onClick={importJSON}
                className="flex items-center gap-1 px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
              >
                <Upload size={16} />
                Import
              </button>
              <button
                onClick={exportJSON}
                className="flex items-center gap-1 px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700"
              >
                <Save size={16} />
                Export
              </button>
            </div>
            <input
              ref={fileInputRef}
              type="file"
              accept=".json"
              onChange={handleFileImport}
              className="hidden"
            />
          </div>

          {viewMode === 'json' ? (
            <div className="flex-1 p-4">
              <textarea
                value={JSON.stringify(resumeData, null, 2)}
                onChange={(e) => {
                  try {
                    setResumeData(JSON.parse(e.target.value));
                  } catch (error) {
                    // Invalid JSON, don't update
                  }
                }}
                className="w-full h-full border border-gray-300 rounded-lg p-4 font-mono text-sm resize-none focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          ) : (
            <div className="flex flex-1 overflow-hidden">
              {/* Section Navigation */}
              <div className="w-48 bg-gray-50 border-r">
                <nav className="p-4">
                  {Object.entries(sectionIcons).map(([section, Icon]) => (
                    <button
                      key={section}
                      onClick={() => setActiveSection(section)}
                      className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg mb-2 transition-colors text-sm ${
                        activeSection === section
                          ? 'bg-blue-100 text-blue-700'
                          : 'text-gray-600 hover:bg-gray-100'
                      }`}
                    >
                      <Icon size={16} />
                      <span className="capitalize">{section}</span>
                    </button>
                  ))}
                </nav>
              </div>

              {/* Editor Content */}
              <div className="flex-1 overflow-y-auto">
                {/* Messages */}
                {importError && (
                  <div className="mx-4 mt-4 bg-red-100 border border-red-400 text-red-700 px-4 py-2 rounded text-sm">
                    {importError}
                  </div>
                )}
                {importSuccess && (
                  <div className="mx-4 mt-4 bg-green-100 border border-green-400 text-green-700 px-4 py-2 rounded text-sm">
                    {importSuccess}
                  </div>
                )}

                <div className="p-6">
                  {activeSection === 'basics' && (
                    <div>
                      <h2 className="text-xl font-bold mb-4 flex items-center">
                        <User className="mr-2" size={20} />
                        Personal Information
                      </h2>
                      <div className="grid grid-cols-1 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Name</label>
                          <input
                            type="text"
                            value={resumeData.basics.name}
                            onChange={(e) => updateBasics('name', e.target.value)}
                            className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                          <input
                            type="email"
                            value={resumeData.basics.email}
                            onChange={(e) => updateBasics('email', e.target.value)}
                            className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Phone</label>
                          <input
                            type="tel"
                            value={resumeData.basics.phone}
                            onChange={(e) => updateBasics('phone', e.target.value)}
                            className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Address</label>
                          <input
                            type="text"
                            value={resumeData.basics.location.address}
                            onChange={(e) => updateBasics('address', e.target.value)}
                            className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          />
                        </div>
                      </div>
                    </div>
                  )}

                  {activeSection === 'education' && (
                    <div>
                      <div className="flex justify-between items-center mb-4">
                        <h2 className="text-xl font-bold flex items-center">
                          <GraduationCap className="mr-2" size={20} />
                          Education
                        </h2>
                        <button
                          onClick={addEducation}
                          className="bg-blue-600 text-white px-3 py-1 rounded hover:bg-blue-700 flex items-center text-sm"
                        >
                          <Plus size={16} className="mr-1" />
                          Add Education
                        </button>
                      </div>
                      {resumeData.education.map((edu, index) => (
                        <div key={index} className="border border-gray-200 rounded-lg p-4 mb-4">
                          <div className="flex justify-between items-start mb-3">
                            <h3 className="font-semibold">Education {index + 1}</h3>
                            <button
                              onClick={() => removeEducation(index)}
                              className="text-red-600 hover:text-red-800"
                            >
                              <Trash2 size={16} />
                            </button>
                          </div>
                          <div className="grid grid-cols-1 gap-3">
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-1">Institution</label>
                              <input
                                type="text"
                                value={edu.institution}
                                onChange={(e) => updateEducation(index, 'institution', e.target.value)}
                                className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                              />
                            </div>
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-1">Degree Type</label>
                              <input
                                type="text"
                                value={edu.studyType}
                                onChange={(e) => updateEducation(index, 'studyType', e.target.value)}
                                className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                              />
                            </div>
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-1">Field of Study</label>
                              <input
                                type="text"
                                value={edu.area}
                                onChange={(e) => updateEducation(index, 'area', e.target.value)}
                                className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                              />
                            </div>
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                              <input
                                type="text"
                                value={edu.endDate}
                                onChange={(e) => updateEducation(index, 'endDate', e.target.value)}
                                className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                              />
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}

                  {activeSection === 'work' && (
                    <div>
                      <div className="flex justify-between items-center mb-4">
                        <h2 className="text-xl font-bold flex items-center">
                          <Briefcase className="mr-2" size={20} />
                          Work Experience
                        </h2>
                        <button
                          onClick={addWork}
                          className="bg-blue-600 text-white px-3 py-1 rounded hover:bg-blue-700 flex items-center text-sm"
                        >
                          <Plus size={16} className="mr-1" />
                          Add Job
                        </button>
                      </div>
                      {resumeData.work.map((job, jobIndex) => (
                        <div key={jobIndex} className="border border-gray-200 rounded-lg p-4 mb-4">
                          <div className="flex justify-between items-start mb-3">
                            <h3 className="font-semibold">Job {jobIndex + 1}</h3>
                            <button
                              onClick={() => removeWork(jobIndex)}
                              className="text-red-600 hover:text-red-800"
                            >
                              <Trash2 size={16} />
                            </button>
                          </div>
                          <div className="grid grid-cols-2 gap-3 mb-3">
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-1">Company</label>
                              <input
                                type="text"
                                value={job.company}
                                onChange={(e) => updateWork(jobIndex, 'company', e.target.value)}
                                className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                              />
                            </div>
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-1">Position</label>
                              <input
                                type="text"
                                value={job.position}
                                onChange={(e) => updateWork(jobIndex, 'position', e.target.value)}
                                className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                              />
                            </div>
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                              <input
                                type="text"
                                value={job.startDate}
                                onChange={(e) => updateWork(jobIndex, 'startDate', e.target.value)}
                                className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                              />
                            </div>
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                              <input
                                type="text"
                                value={job.endDate}
                                onChange={(e) => updateWork(jobIndex, 'endDate', e.target.value)}
                                className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                              />
                            </div>
                          </div>
                          <div>
                            <div className="flex justify-between items-center mb-2">
                              <label className="block text-sm font-medium text-gray-700">Highlights</label>
                              <button
                                onClick={() => addWorkHighlight(jobIndex)}
                                className="text-blue-600 hover:text-blue-800 text-sm flex items-center"
                              >
                                <Plus size={14} className="mr-1" />
                                Add Highlight
                              </button>
                            </div>
                            {job.highlights.map((highlight, highlightIndex) => (
                              <div key={highlightIndex} className="flex gap-2 mb-2">
                                <textarea
                                  value={highlight}
                                  onChange={(e) => updateWorkHighlight(jobIndex, highlightIndex, e.target.value)}
                                  className="flex-1 p-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 min-h-[60px]"
                                  rows={2}
                                />
                                <button
                                  onClick={() => removeWorkHighlight(jobIndex, highlightIndex)}
                                  className="text-red-600 hover:text-red-800"
                                >
                                  <Trash2 size={14} />
                                </button>
                              </div>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}

                  {activeSection === 'skills' && (
                    <div>
                      <div className="flex justify-between items-center mb-4">
                        <h2 className="text-xl font-bold flex items-center">
                          <Code className="mr-2" size={20} />
                          Skills
                        </h2>
                        <button
                          onClick={addSkillCategory}
                          className="bg-blue-600 text-white px-3 py-1 rounded hover:bg-blue-700 flex items-center text-sm"
                        >
                          <Plus size={16} className="mr-1" />
                          Add Category
                        </button>
                      </div>
                      {resumeData.skills.map((skillCategory, categoryIndex) => (
                        <div key={categoryIndex} className="border border-gray-200 rounded-lg p-4 mb-4">
                          <div className="flex justify-between items-start mb-3">
                            <div className="flex-1 mr-4">
                              <label className="block text-sm font-medium text-gray-700 mb-1">Category Name</label>
                              <input
                                type="text"
                                value={skillCategory.name}
                                onChange={(e) => updateSkillCategory(categoryIndex, 'name', e.target.value)}
                                className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                              />
                            </div>
                            <button
                              onClick={() => removeSkillCategory(categoryIndex)}
                              className="text-red-600 hover:text-red-800 mt-6"
                            >
                              <Trash2 size={16} />
                            </button>
                          </div>
                          <div>
                            <div className="flex justify-between items-center mb-2">
                              <label className="block text-sm font-medium text-gray-700">Skills</label>
                              <button
                                onClick={() => addSkillKeyword(categoryIndex)}
                                className="text-blue-600 hover:text-blue-800 text-sm flex items-center"
                              >
                                <Plus size={14} className="mr-1" />
                                Add Skill
                              </button>
                            </div>
                            {skillCategory.keywords.map((keyword, keywordIndex) => (
                              <div key={keywordIndex} className="flex gap-2 mb-2">
                                <input
                                  type="text"
                                  value={keyword}
                                  onChange={(e) => updateSkillKeyword(categoryIndex, keywordIndex, e.target.value)}
                                  className="flex-1 p-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                />
                                <button
                                  onClick={() => removeSkillKeyword(categoryIndex, keywordIndex)}
                                  className="text-red-600 hover:text-red-800"
                                >
                                  <Trash2 size={14} />
                                </button>
                              </div>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}

                  {activeSection === 'projects' && (
                    <div>
                      <div className="flex justify-between items-center mb-4">
                        <h2 className="text-xl font-bold flex items-center">
                          <FileText className="mr-2" size={20} />
                          Projects
                        </h2>
                        <button
                          onClick={addProject}
                          className="bg-blue-600 text-white px-3 py-1 rounded hover:bg-blue-700 flex items-center text-sm"
                        >
                          <Plus size={16} className="mr-1" />
                          Add Project
                        </button>
                      </div>
                      {resumeData.projects.map((project, index) => (
                        <div key={index} className="border border-gray-200 rounded-lg p-4 mb-4">
                          <div className="flex justify-between items-start mb-3">
                            <h3 className="font-semibold">Project {index + 1}</h3>
                            <button
                              onClick={() => removeProject(index)}
                              className="text-red-600 hover:text-red-800"
                            >
                              <Trash2 size={16} />
                            </button>
                          </div>
                          <div className="grid grid-cols-1 gap-3 mb-3">
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-1">Project Name</label>
                              <input
                                type="text"
                                value={project.name}
                                onChange={(e) => updateProject(index, 'name', e.target.value)}
                                className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                              />
                            </div>
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-1">URL (optional)</label>
                              <input
                                type="url"
                                value={project.url || ''}
                                onChange={(e) => updateProject(index, 'url', e.target.value)}
                                className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                              />
                            </div>
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                            <textarea
                              value={project.description}
                              onChange={(e) => updateProject(index, 'description', e.target.value)}
                              className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 min-h-[80px]"
                              rows={3}
                            />
                          </div>
                        </div>
                      ))}
                    </div>
                  )}

                  {activeSection === 'awards' && (
                    <div>
                      <div className="flex justify-between items-center mb-4">
                        <h2 className="text-xl font-bold flex items-center">
                          <Award className="mr-2" size={20} />
                          Awards & Achievements
                        </h2>
                        <button
                          onClick={() => setResumeData(prev => ({
                            ...prev,
                            awards: [...prev.awards, { title: "", awarder: "", date: "", summary: "" }]
                          }))}
                          className="bg-blue-600 text-white px-3 py-1 rounded hover:bg-blue-700 flex items-center text-sm"
                        >
                          <Plus size={16} className="mr-1" />
                          Add Award
                        </button>
                      </div>
                      {resumeData.awards.map((award, index) => (
                        <div key={index} className="border border-gray-200 rounded-lg p-4 mb-4">
                          <div className="flex justify-between items-start mb-3">
                            <h3 className="font-semibold">Award {index + 1}</h3>
                            <button
                              onClick={() => setResumeData(prev => ({
                                ...prev,
                                awards: prev.awards.filter((_, i) => i !== index)
                              }))}
                              className="text-red-600 hover:text-red-800"
                            >
                              <Trash2 size={16} />
                            </button>
                          </div>
                          <div className="grid grid-cols-2 gap-3 mb-3">
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-1">Title</label>
                              <input
                                type="text"
                                value={award.title}
                                onChange={(e) => setResumeData(prev => ({
                                  ...prev,
                                  awards: prev.awards.map((a, i) => 
                                    i === index ? { ...a, title: e.target.value } : a
                                  )
                                }))}
                                className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                              />
                            </div>
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-1">Awarder</label>
                              <input
                                type="text"
                                value={award.awarder}
                                onChange={(e) => setResumeData(prev => ({
                                  ...prev,
                                  awards: prev.awards.map((a, i) => 
                                    i === index ? { ...a, awarder: e.target.value } : a
                                  )
                                }))}
                                className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                              />
                            </div>
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">Summary</label>
                            <textarea
                              value={award.summary}
                              onChange={(e) => setResumeData(prev => ({
                                ...prev,
                                awards: prev.awards.map((a, i) => 
                                  i === index ? { ...a, summary: e.target.value } : a
                                )
                              }))}
                              className="w-full p-2 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 min-h-[60px]"
                              rows={2}
                            />
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Right Panel - PDF Preview */}
        <div className="w-1/2 bg-white shadow-lg flex flex-col">
          <div className="h-16 bg-gray-50 border-b flex items-center justify-between px-6">
            <h2 className="text-xl font-bold text-gray-800">PDF Preview</h2>
            <div className="flex gap-2">
              {generatedLatex && (
                <button
                  onClick={downloadLatex}
                  className="flex items-center gap-1 px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700"
                >
                  <Download size={16} />
                  LaTeX
                </button>
              )}
              {showPDF && (
                <PDFDownloadLink
                  document={<ResumePDF data={resumeData} />}
                  fileName="resume.pdf"
                  className="flex items-center gap-1 px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700"
                >
                  {({ blob, url, loading, error }) =>
                    loading ? (
                      <>
                        <Download size={16} />
                        Loading...
                      </>
                    ) : (
                      <>
                        <Download size={16} />
                        PDF
                      </>
                    )
                  }
                </PDFDownloadLink>
              )}
              <button
                onClick={handleGeneratePDF}
                className="flex items-center gap-1 px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
              >
                <Eye size={16} />
                Generate
              </button>
            </div>
          </div>
          
          <div className="flex-1 bg-gray-100">
            {showPDF ? (
              <PDFViewer style={{ width: '100%', height: '100%', border: 'none' }}>
                <ResumePDF data={resumeData} />
              </PDFViewer>
            ) : (
              <div className="h-full flex items-center justify-center text-center text-gray-500">
                <div>
                  <FileText size={64} className="mx-auto mb-4 text-gray-400" />
                  <p className="text-lg font-medium">No PDF Generated</p>
                  <p className="text-sm">Click "Generate" to see preview</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResumeLatexGenerator;